<!-- Generated by SpecStory -->

# 创建ZTE产品检索系统架构图 (2025-08-08 05:46Z)

_**User**_

请使用PlantUML为ZTE产品检索系统创建一套详细的系统架构图。要求如下：

1. **架构图类型**：创建多个不同视角的架构图，包括：
   - 系统整体架构图
   - 技术组件架构图
   - 业务流程架构图
   - 数据流架构图
   - 部署架构图

2. **设计要求**：
   - 使用PlantUML语法编写
   - 采用分层架构设计理念
   - 体现系统的核心特性：多路检索(ES+Milvus+KG)、AI驱动(BGE-M3+NebulaBiz LLM)、智能问答
   - 包含详细的组件说明和数据流向
   - 使用中文标注，确保可读性

3. **技术深度**：
   - 深入分析现有代码库结构
   - 准确反映实际的技术栈和组件关系
   - 包含外部服务依赖(UAC认证、Apollo配置中心等)
   - 展示监控和运维体系

4. **文件组织**：
   - 将生成的所有.puml文件保存到目录：`doc\架构\cursor-gpt5-new-detail\`
   - 为每个架构图创建独立的文件
   - 创建README.md文档说明各架构图的用途和查看顺序

5. **质量标准**：
   - 架构图应该专业、清晰、易于理解
   - 适合用于技术汇报、团队培训和系统文档
   - 确保架构图与实际代码实现保持一致

---

_**Assistant**_



---

