# InfoGo图谱数据导入API接口规范

## 概述

本文档定义了InfoGo系统向知识图谱导入数据的API接口规范，包括实体批量导入和关系批量导入两个核心接口。

### 设计原则
- **幂等性**: 重复调用相同请求应产生相同结果
- **原子性**: 单次操作要么全部成功，要么全部失败
- **一致性**: 保证图谱数据的完整性和一致性
- **高效性**: 支持批量操作，减少网络开销

---

## 1. 基础配置

### 1.1 服务端点
```
Base URL: https://kg-import.zte.com.cn/api/v1
```

### 1.2 认证方式
```http
Headers:
  Content-Type: application/json
  Authorization: Bearer {access_token}
  X-System-Name: InfoGo
  X-Request-ID: {unique_request_id}
```

### 1.3 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-08-13",
  "request_id": "req_12345678"
}
```

---

## 2. 批量实体导入接口

### 2.1 接口信息
- **URL**: `POST /entities/batch`
- **描述**: 批量导入多个实体到知识图谱

### 2.2 请求参数说明

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| space | string | 是 | 图谱空间名称 | "product_multiversion" |
| batch_id | string | 是 | 批次唯一标识，用于追踪导入任务 | "batch_20240813_001" |
| entities | array | 是 | 实体列表 | 见下方entities字段说明 |
| operation | string | 否 | 操作类型，默认为INSERT_OR_UPDATE | "INSERT_OR_UPDATE" |
| options | object | 否 | 导入选项配置 | 见下方options字段说明 |

#### entities数组元素说明

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 实体唯一标识 | "product_123456" |
| label | string | 是 | 实体标签类型 | "product"、"series"、"document"、"text"、"table"、"product_version" |
| properties | object | 是 | 实体属性集合 | 见下方不同实体类型的properties说明 |

#### 产品实体(product)的properties字段

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| name | string | 是 | 产品名称 | "ZXR10 M6000-8S Plus" |
| product_id | string | 是 | 产品ID | "product_123456" |
| series_id | string | 否 | 所属系列ID | "series_789" |
| created_time | string | 否 | 创建时间，格式yyyy-MM-dd | "2024-08-13" |
| updated_time | string | 否 | 更新时间，格式yyyy-MM-dd | "2024-08-13" |
| source_system | string | 否 | 来源系统 | "InfoGo" |

#### 系列实体(series)的properties字段

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| name | string | 是 | 系列名称 | "ZXR10 M6000系列" |
| series_id | string | 是 | 系列ID | "series_789" |
| created_time | string | 否 | 创建时间，格式yyyy-MM-dd | "2024-08-13" |
| updated_time | string | 否 | 更新时间，格式yyyy-MM-dd | "2024-08-13" |
| source_system | string | 否 | 来源系统 | "InfoGo" |

#### 版本实体(product_version)的properties字段

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| version | string | 是 | 版本号 | "V2.1" |
| release_date | string | 否 | 发布日期，格式yyyy-MM-dd | "2024-01-15" |
| created_time | string | 否 | 创建时间，格式yyyy-MM-dd | "2024-08-13" |
| updated_time | string | 否 | 更新时间，格式yyyy-MM-dd | "2024-08-13" |
| source_system | string | 否 | 来源系统 | "InfoGo" |

#### 文档实体(document/text/table)的properties字段

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| content | string | 是 | 文档内容或名称 | "用户手册" |
| document_name | string | 否 | 文档文件名 | "ZXR10_M6000_用户手册.pdf" |
| created_time | string | 否 | 创建时间，格式yyyy-MM-dd | "2024-08-13" |
| updated_time | string | 否 | 更新时间，格式yyyy-MM-dd | "2024-08-13" |
| source_system | string | 否 | 来源系统 | "InfoGo" |

#### options配置说明

| 字段名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| ignore_duplicates | boolean | 否 | 是否忽略重复数据 | true |
| validate_schema | boolean | 否 | 是否进行Schema校验 | true |
| transaction_mode | boolean | 否 | 是否启用事务模式 | true |

### 2.3 请求示例
```json
{
  "space": "product_multiversion",
  "batch_id": "batch_20240813_001",
  "entities": [
    {
      "id": "product_123456",
      "label": "product",
      "properties": {
        "name": "ZXR10 M6000-8S Plus",
        "product_id": "product_123456",
        "series_id": "series_789",
        "created_time": "2024-08-13",
        "source_system": "InfoGo"
      }
    },
    {
      "id": "series_789",
      "label": "series", 
      "properties": {
        "name": "ZXR10 M6000系列",
        "series_id": "series_789",
        "created_time": "2024-08-13",
        "source_system": "InfoGo"
      }
    },
    {
      "id": "version_001",
      "label": "product_version",
      "properties": {
        "version": "V2.1",
        "release_date": "2024-01-15",
        "created_time": "2024-08-13",
        "source_system": "InfoGo"
      }
    }
  ],
  "operation": "INSERT_OR_UPDATE",
  "options": {
    "ignore_duplicates": true,
    "validate_schema": true,
    "transaction_mode": true
  }
}
```

### 2.4 响应参数说明

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | int | 响应状态码，200表示成功 | 200 |
| message | string | 响应消息 | "Batch import completed" |
| data | object | 响应数据 | 见下方data字段说明 |
| timestamp | string | 响应时间，格式yyyy-MM-dd | "2024-08-13" |
| request_id | string | 请求ID | "req_12345679" |

#### data字段说明

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| batch_id | string | 批次ID | "batch_20240813_001" |
| total_count | int | 总实体数量 | 3 |
| success_count | int | 成功导入数量 | 3 |
| failed_count | int | 失败数量 | 0 |
| failed_entities | array | 失败的实体列表，包含实体ID和错误原因 | [] |
| execution_time_ms | int | 执行耗时（毫秒） | 1500 |

### 2.5 响应示例
```json
{
  "code": 200,
  "message": "Batch import completed",
  "data": {
    "batch_id": "batch_20240813_001",
    "total_count": 3,
    "success_count": 3,
    "failed_count": 0,
    "failed_entities": [],
    "execution_time_ms": 1500
  },
  "timestamp": "2024-08-13",
  "request_id": "req_12345679"
}
```

---

## 3. 批量关系导入接口

### 3.1 接口信息
- **URL**: `POST /relationships/batch`
- **描述**: 批量导入多个关系到知识图谱

### 3.2 请求参数说明

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| space | string | 是 | 图谱空间名称 | "product_multiversion" |
| batch_id | string | 是 | 批次唯一标识，用于追踪导入任务 | "rel_batch_20240813_001" |
| relationships | array | 是 | 关系列表 | 见下方relationships字段说明 |
| operation | string | 否 | 操作类型，默认为INSERT_OR_UPDATE | "INSERT_OR_UPDATE" |
| options | object | 否 | 导入选项配置 | 见下方options字段说明 |

#### relationships数组元素说明

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | string | 否 | 关系唯一标识，不传则自动生成 | "rel_123456" |
| type | string | 是 | 关系类型 | 见下方关系类型说明 |
| source_entity | object | 是 | 源实体信息 | {"id": "product_123456", "label": "product"} |
| target_entity | object | 是 | 目标实体信息 | {"id": "doc_001", "label": "document"} |
| properties | object | 否 | 关系属性 | 见下方properties字段说明 |

#### 支持的关系类型

| 关系类型 | 描述 | 源实体类型 | 目标实体类型 |
|----------|------|------------|-------------|
| product_document_relation | 产品-文档关系 | product | document/text/table |
| product_version_relation | 产品-版本关系 | product | product_version |
| series_document_relation | 系列-文档关系 | series | document/text/table |
| series_product_relation | 系列-产品关系 | series | product |
| series_version_relation | 系列-版本关系 | series | product_version |
| productversion_document_relation | 版本-文档关系 | product_version | document/text/table |
| document_text_relation | 文档-文本关系 | document | text |
| document_table_relation | 文档-表格关系 | document | table |
| document_chapter_relation | 文档-章节关系 | document | chapter |
| text_table_relation | 文本-表格关系 | text | table |

#### source_entity和target_entity字段说明

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 实体ID | "product_123456" |
| label | string | 是 | 实体标签类型 | "product" |

#### 关系properties字段说明

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| priority | int | 否 | 关系优先级，数值越小优先级越高 | 1 |
| relation_type | string | 否 | 关系子类型 | "primary_document" |
| is_current_version | boolean | 否 | 是否为当前版本（仅用于版本关系） | true |
| created_time | string | 否 | 创建时间，格式yyyy-MM-dd | "2024-08-13" |
| updated_time | string | 否 | 更新时间，格式yyyy-MM-dd | "2024-08-13" |
| source_system | string | 否 | 来源系统 | "InfoGo" |

#### options配置说明

| 字段名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| validate_entities | boolean | 否 | 是否校验实体是否存在 | true |
| create_missing_entities | boolean | 否 | 是否自动创建不存在的实体 | false |
| transaction_mode | boolean | 否 | 是否启用事务模式 | true |

### 3.3 请求示例
```json
{
  "space": "product_multiversion",
  "batch_id": "rel_batch_20240813_001",
  "relationships": [
    {
      "id": "rel_123456",
      "type": "product_document_relation",
      "source_entity": {"id": "product_123456", "label": "product"},
      "target_entity": {"id": "doc_001", "label": "document"},
      "properties": {
        "relation_type": "primary_document",
        "priority": 1,
        "created_time": "2024-08-13",
        "source_system": "InfoGo"
      }
    },
    {
      "type": "product_version_relation",
      "source_entity": {"id": "product_123456", "label": "product"},
      "target_entity": {"id": "version_001", "label": "product_version"},
      "properties": {
        "is_current_version": true,
        "created_time": "2024-08-13",
        "source_system": "InfoGo"
      }
    },
    {
      "type": "series_product_relation",
      "source_entity": {"id": "series_789", "label": "series"},
      "target_entity": {"id": "product_123456", "label": "product"},
      "properties": {
        "priority": 1,
        "created_time": "2024-08-13",
        "source_system": "InfoGo"
      }
    }
  ],
  "operation": "INSERT_OR_UPDATE",
  "options": {
    "validate_entities": true,
    "create_missing_entities": false,
    "transaction_mode": true
  }
}
```

### 3.4 响应参数说明

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | int | 响应状态码，200表示成功 | 200 |
| message | string | 响应消息 | "Batch relationships import completed" |
| data | object | 响应数据 | 见下方data字段说明 |
| timestamp | string | 响应时间，格式yyyy-MM-dd | "2024-08-13" |
| request_id | string | 请求ID | "req_12345680" |

#### data字段说明

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| batch_id | string | 批次ID | "rel_batch_20240813_001" |
| total_count | int | 总关系数量 | 3 |
| success_count | int | 成功导入数量 | 3 |
| failed_count | int | 失败数量 | 0 |
| failed_relationships | array | 失败的关系列表，包含关系信息和错误原因 | [] |
| execution_time_ms | int | 执行耗时（毫秒） | 1200 |

### 3.5 响应示例
```json
{
  "code": 200,
  "message": "Batch relationships import completed",
  "data": {
    "batch_id": "rel_batch_20240813_001",
    "total_count": 3,
    "success_count": 3,
    "failed_count": 0,
    "failed_relationships": [],
    "execution_time_ms": 1200
  },
  "timestamp": "2024-08-13",
  "request_id": "req_12345680"
}
```

---

## 4. 错误处理

### 4.1 错误代码定义

| 错误代码 | 错误类型 | 描述 | 解决方案 |
|----------|----------|------|----------|
| 4001 | INVALID_SCHEMA | 数据格式不符合Schema | 检查数据格式和字段类型 |
| 4002 | ENTITY_NOT_FOUND | 实体不存在 | 先创建相关实体 |
| 4003 | DUPLICATE_KEY | 主键重复 | 使用UPDATE操作或更改ID |
| 4004 | CONSTRAINT_VIOLATION | 约束违反 | 检查数据完整性约束 |
| 5001 | GRAPH_DB_ERROR | 图数据库错误 | 检查数据库连接和配置 |
| 5002 | TIMEOUT_ERROR | 操作超时 | 减少批量大小或重试 |
| 5003 | TRANSACTION_FAILED | 事务失败 | 检查数据一致性后重试 |

### 4.2 错误响应示例
```json
{
  "code": 4001,
  "message": "数据格式不符合Schema",
  "data": {
    "error_details": [
      {
        "entity_id": "product_123456",
        "field": "name",
        "error": "字段不能为空"
      }
    ]
  },
  "timestamp": "2024-08-13",
  "request_id": "req_12345681"
}
```

---

## 5. 使用说明

### 5.1 导入流程建议
1. **先导入实体**: 使用批量实体导入接口导入所有相关实体
2. **再导入关系**: 使用批量关系导入接口建立实体间的关系
3. **校验结果**: 检查返回的成功/失败统计，处理失败的数据

### 5.2 性能建议
- **批量大小**: 建议单次批量操作不超过1000个实体/关系
- **并发控制**: 控制并发请求数量，避免数据库压力过大
- **数据预处理**: 在客户端进行数据去重和格式校验

### 5.3 注意事项
- 所有日期字段统一使用 **yyyy-MM-dd** 格式
- 实体ID在图谱中必须全局唯一
- 关系导入时确保源实体和目标实体已存在
- 启用事务模式确保数据一致性

---

*最后更新: 2024-08-13*
*版本: 1.0*