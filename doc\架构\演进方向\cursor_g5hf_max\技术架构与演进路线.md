# 产品技术问答系统 技术架构与演进路线（2025Q3-Q4）

版本：v1.0  
日期：2025-08-08  
范围：面向 BN / OTN / FM 多产品线的统一问答平台

---

## 一、总体技术架构

```plantuml
@startuml
!theme plain
left to right direction

skinparam rectangle BackgroundColor White
skinparam rectangle BorderColor Black

package "数据源" #AliceBlue {
  file "TSM 文档库" as TSM
  database "Info-Go" as INFO
}

package "采集与前置处理" #LightGreen {
  component "Ingestor\n(调度/增量)" as ING
  component "Parser/OCR\n(Word/PDF/PPT/Excel/图片)" as PAR
  component "Cleaner\n(去重/歧义/规范化)" as CLN
  component "Summarizer/Structurer\n(摘要提纯/结构化抽取)" as SUM
  component "Tiering\n(热/温/冷)" as TIER
  component "Indexer\n(ES/向量/KG/DB)" as IDX
}

package "存储层" #Moccasin {
  database "Elasticsearch\n(关键字/稀疏语义)" as ES
  database "向量库\n(密集语义/BGE)" as VEC
  database "知识图谱\n(实体/关系)" as KG
  database "元数据/ACL/画像\n(DB)" as META
}

package "检索与生成" #Wheat {
  component "Query Rewrite\n(语义改写/消歧)" as QR
  component "Retriever\n(稀疏+密集+KG 多路召回)" as RET
  component "Reranker(BGE)\n(交叉编码器)" as RR
  component "Fusion\n(融合打分)" as FUS
  component "AnswerGen\n(RAG+事实校验)" as GEN
}

package "缓存与预加载" #Cornsilk {
  component "Semantic Cache\n(语义/检索结果)" as SC
  component "Chunk Cache\n(热点切片)" as CC
  component "Preloader\n(基于画像/历史预测)" as PRE
}

package "评估与运营" #Thistle {
  component "Evaluation\n(召回/重排/生成)" as EVAL
  component "AB 实验/监控\n(性能/体验/SLA)" as EXP
  component "行为分析\n(漏斗/高频场景)" as UBA
  component "FAQ 管理\n(人工补全/推荐)" as FAQ
}

package "接入与安全" #Beige {
  component "API Gateway\n(限流/熔断/路由)" as GW
  component "Auth/ACL\n(SSO/OIDC/ABAC)" as AUTH
}

TSM --> ING
INFO --> ING
ING --> PAR
PAR --> CLN
CLN --> SUM
SUM --> TIER
TIER --> IDX
IDX --> ES
IDX --> VEC
IDX --> KG
IDX --> META

GW --> AUTH
AUTH --> QR
QR --> PRE
PRE --> SC
QR --> RET
SC --> RET
RET --> RR
RR --> FUS
FUS --> GEN
GEN --> GW

EVAL .. RET
EVAL .. RR
EVAL .. GEN
EXP .. GW
UBA .. GW
FAQ .. GEN
SC .. EXP
CC .. RET

note bottom of TIER
  分层策略：
  - 热：近30天、高频文档
  - 温：近6个月、常见文档
  - 冷：长期低频
  存储/索引/缓存策略随层级调整
end note

note bottom of RET
  多路召回：
  1) 关键字/BM25/稀疏语义
  2) 密集语义（BGE 向量）
  3) 知识图谱关系检索
end note

@enduml
```

要点：
- 前置处理将“去重/消歧/摘要提纯/结构化抽取/冷热分层”纳入统一流水线，保证索引质量与查询时延。
- 检索采用“稀疏+密集+KG”多路召回，交叉编码器重排，事实校验兜底，输出可点击引用角标。
- 引入“语义缓存+热点切片缓存+预加载”，在不牺牲新鲜度的前提下降低端到端时延。
- 评估与运营贯穿全链路，提供离线基准与在线 A/B，驱动持续优化。

### 1.1 术语说明（Glossary）
- **Ingestor（采集器）/ Ingestor**: 负责从 `TSM`、`Info-Go` 等数据源调度全量/增量数据进入流水线。
- **Parser/OCR（解析/OCR）/ Parser & OCR**: 解析 `Word/PDF/PPT/Excel/图片` 等格式，抽取文本、表格与结构信息；对扫描件执行 OCR。
- **Cleaner（清洗）/ Cleaner**: 去重、歧义处理、规范化单位/术语，提升数据一致性与可用性。
- **Summarizer/Structurer（摘要/结构化）/ Summarizer & Structurer**: 生成章节摘要、关键词与结构化字段，支撑查询改写与重排特征。
- **Tiering（冷热分层）/ Tiering**: 按访问频度与时效性将数据划分为 热/温/冷，用以决定索引刷新、缓存与存储策略。
- **Indexer（索引器）/ Indexer**: 将切片写入 `Elasticsearch`、向量库、知识图谱与元数据库。
- **Elasticsearch（ES）/ Elasticsearch**: 关键字检索与稀疏语义检索引擎（如 BM25/SPLADE）。
- **向量库 / Vector Store**: 存储密集语义向量（如 BGE Embeddings），用于近邻召回。
- **知识图谱（KG）/ Knowledge Graph**: 承载实体-关系（如 产品-版本-命令-指标-案例）以支持关系检索与消歧。
- **元数据/ACL/画像（DB）/ Metadata DB**: 存放文档元信息、访问控制标签与用户画像。
- **Query Rewrite（查询改写）/ Query Rewrite**: 对用户问题进行语义改写与消歧，增强检索可召回性与精准度。
- **Retriever（检索器）/ Retriever**: 多路召回（稀疏+密集+KG），形成候选集。
- **Reranker（重排器）/ Reranker (Cross-Encoder)**: 对候选集进行精排打分，提升前列结果的相关性。
- **Fusion（融合打分）/ Fusion**: 结合多源通道与特征进行分数融合。
- **AnswerGen（答案生成）/ Answer Generation (RAG)**: 基于检索证据进行生成并做事实一致性校验。
- **Semantic Cache（语义缓存）/ Semantic Cache**: 按语义哈希缓存查询与检索结果，降低重复查询时延。
- **Chunk Cache（切片缓存）/ Chunk Cache**: 缓存高频文档切片，减少热点读取成本。
- **Preloader（预加载）/ Preloader**: 基于画像/历史预测下一问，提前将检索候选写入缓存。
- **Evaluation（评估）/ Evaluation**: 覆盖召回、重排、生成质量与性能指标，提供离线与在线评测。
- **AB 实验/监控 / A/B Testing & Monitoring**: 面向策略/权重/提示词的灰度与对照实验，观测 SLA 与体验。
- **行为分析（UBA）/ Behavior Analytics**: 归集点击/追问等行为做漏斗与高频场景分析。
- **FAQ 管理 / FAQ Management**: 结合缺口发现与人工补全，完善知识库可覆盖性。
- **API Gateway（网关）/ API Gateway**: 统一流量入口，提供限流、熔断与路由。
- **Auth/ACL（认证与鉴权）/ Auth & ACL**: 基于 `SSO/OIDC/ABAC` 的身份认证与访问控制。
- **BM25/SPLADE**: 稀疏检索方法与模型；与密集向量召回互补。
- **BGE（Embedding/Cross-Encoder）/ BGE**: 领域向量化与交叉编码器模型，用于召回与重排。
- **RAG（检索增强生成）/ Retrieval-Augmented Generation**: 以检索证据增强生成的方式，保障可溯源与事实性。

---

## 二、关键改造项与技术要点

- 数据前置处理：
  - 预处理（AI+人工标注）：识别标题层级、表格/列表抽取、命令/参数结构化；图片/扫描件 OCR；单位/术语标准化。
  - 数据分层（热/温/冷）：结合访问频度与时效性，分层决定索引刷新频率、缓存策略与副本数。
  - 结构化存储：图谱承载“产品-版本-特性-命令-指标-案例”关系，DB 承载元数据、ACL、用户画像。
  - 文档摘要-结构化提纯：为长文生成多粒度摘要与章节关键词，用于 Query Rewrite 与重排特征。

- 数据清洗：
  - 去重：文档级（版本/哈希）、切片级（语义相似度阈值）；
  - 歧义处理：同名产品/指标消歧（上下文+图谱路径+组织视角）。

- 检索增强：
  - 稀疏语义（BM25/SPLADE 可选）+ 密集语义（BGE 向量），加权融合；
  - 智能预加载：基于用户画像/历史对话/热门主题预测查询意图，提前计算召回与重排候选并进入缓存。

- 评估框架：
  - 自动化评测：相关性、精确度、事实性、可读性；
  - 性能指标：P95 首字、端到端延迟、缓存命中率、资源成本；
  - 用户体验：CSAT、点击引用率、追问率。

- 后置处理：
  - 重排优化：领域数据微调；特征引入（章节位置、标题匹配、图谱距离、摘要覆盖率）。
  - 缓存优化：语义哈希一致性、结果有效期分层、冷热切片自动晋升；
  - 人工 FAQ 补全：提供缺口发现与推荐，结合编辑工作台；
  - 用户行为分析：路径漏斗、高频问题簇、失败模式定位，驱动专项精调与高频场景模板化。

---

## 三、关键流程时序（含预加载） / Key Process Sequence (with Preloading)

```plantuml
@startuml
actor "用户（User）" as User
participant GW as "API 网关（API Gateway）"
participant AUTH as "认证与访问控制（Auth/ACL）"
participant PRE as "预加载（Preloader）"
participant SC as "语义缓存（Semantic Cache）"
participant RET as "检索器（Retriever）"
participant RR as "重排器（Reranker）"
participant GEN as "答案生成（AnswerGen）"
participant EVAL as "评估（Evaluation）"
participant UBA as "行为分析（Behavior Analytics）"

== 会话建立 ==
User -> GW: open_session（开启会话）
GW -> AUTH: verify(token)（验证令牌）
AUTH --> GW: allow(acl, profile)（授权与画像返回）
GW -> PRE: prewarm(profile)（预热）
PRE -> SC: prime(predicted_queries)（写入预测候选）

== 用户提问 ==
User -> GW: ask(question)（用户提问）
GW -> SC: lookup(semantic_hash)（语义哈希查询）
SC --> GW: hit/miss（命中/未命中）
GW -> RET: retrieve(q, acl)（检索）  
RET -> RR: rank(candidates)（重排）
RR --> RET: ranked（已排序）
RET -> GEN: generate(topK, citations)（生成答案与引用）
GEN --> GW: stream(answer)（流式输出）
GW -> EVAL: log(q, answer, metrics)（评估日志）
GW -> UBA: log(behavior)（行为日志）

@enduml
```

### 3.1 步骤说明（中英对照）
- **会话建立 / Session Initialization**
  - 中文: 用户发起 `open_session`。网关调用鉴权服务验证 `token`，返回访问控制（ACL）与用户画像；预加载服务基于画像预测查询并将候选写入语义缓存。
  - English: The user initiates `open_session`. The API Gateway calls Auth to verify the `token`, returns ACL and user profile; the Preloader predicts likely queries and primes the Semantic Cache with candidates.
- **用户提问 / User Query**
  - 中文: 用户提交 `question`。网关基于语义哈希查询 `Semantic Cache`，命中则可直接返回或作为后续候选；未命中进入检索链路。
  - English: The user submits a `question`. The Gateway looks up the `Semantic Cache` by semantic hash; on hit, it can respond directly or reuse candidates; on miss, it proceeds to retrieval.
- **检索与重排 / Retrieval & Reranking**
  - 中文: `Retriever` 执行多路召回（稀疏/密集/KG），交由 `Reranker (Cross-Encoder)` 精排，得到排序结果。
  - English: The `Retriever` performs multi-channel recall (sparse/dense/KG) and passes candidates to the `Reranker (Cross-Encoder)` for precise ranking.
- **生成与流式返回 / Generation & Streaming**
  - 中文: 将 `topK` 与引用传递给 `AnswerGen`，生成答案并以流式返回给网关与用户。
  - English: Pass `topK` items with citations to `AnswerGen`, which generates the answer and streams it back via the Gateway.
- **评估与行为日志 / Evaluation & Behavior Logging**
  - 中文: 网关将问答与性能指标上报 `Evaluation`，并将用户行为写入 `Behavior Analytics` 以供后续分析与优化。
  - English: The Gateway logs Q&A and performance metrics to `Evaluation` and writes behavior events to `Behavior Analytics` for analysis and optimization.
- **预加载命中路径（可选） / Preload-hit Path (Optional)**
  - 中文: 若预加载已将相关候选写入缓存，用户问题可能直接命中 `Semantic Cache`，从而跳过部分检索链路以缩短时延。
  - English: If preloading has primed relevant candidates, the query may hit the `Semantic Cache`, skipping parts of the retrieval chain to reduce latency.

---

## 四、演进路线与里程碑

### 4.1 里程碑一（2025-09-30）

- 数据前置处理：
  - 预处理 MVP：Parser/OCR、标题/段落抽取、基础结构化；
  - 去重与规范化 v1：文档/切片级哈希与近似去重；
  - 分层策略 v1：热/温/冷判定与索引刷新频率配置。
- 检索处理：
  - 稀疏+密集混合检索 v1（BM25 + BGE 向量），可配置权重；
  - 智能预加载 v1：基于热门查询和简单画像的规则预热；
  - 图谱基础检索路径（产品→版本→命令）。
- 后置处理：
  - 重排模型 baseline（BGE Cross-Encoder），网格调参；
  - 缓存 v1：查询结果缓存、热点切片缓存；
  - FAQ 工具 v1：人工补全与引用一键插入。
- 评估与运营：
  - 自动化评测 v1：相关性/精确度/事实一致性离线评测；
  - 在线看板 v1：P95 首字、命中率、CSAT；
  - A/B 能力接入（灰度路由）。
- 验收指标：
  - P95 首字 ≤ 5s；P99 ≤ 8s；缓存命中率 ≥ 25%；
  - 答案质量 ≥ 90% 达 2~3 分；
  - 关键链路稳定性 ≥ 99.9%。

### 4.2 里程碑二（2025-12-30）

- 数据前置处理：
  - 摘要提纯与章节关键词；命令/参数结构化增强；
  - 去重/歧义 v2：语义近邻 + 领域词库；
  - 分层策略 v2：自动晋升/降级与成本感知存储。
- 检索处理：
  - 稀疏语义（可选 SPLADE）与密集语义的自适应融合；
  - 智能预加载 v2：基于用户画像与序列预测（下一问意图）预热；
  - 图谱检索增强：属性约束与多跳路径评分。
- 后置处理：
  - 重排模型领域精调（少量标注集）；特征扩展（章节/摘要/图谱距离）；
  - 缓存 v2：语义缓存一致性、分层 TTL、近线重算；
  - FAQ 推荐：基于缺口检测与点击行为自动建议。
- 评估与运营：
  - 自动化评测 v2：覆盖用户体验主观题；
  - A/B 扩展：策略/权重/提示词多维实验；
  - 成本与SLA优化：同等SLA下降 15% 资源成本。
- 验收指标：
  - P95 首字 ≤ 4s；缓存命中率 ≥ 40%；
  - 事实性校验通过率 ≥ 95%；
  - 高频场景（前20类）一次命中率 ≥ 85%。

---

## 五、风险与依赖

- 上游依赖：TSM/Info-Go 增量可用性与元数据完整性；
- 模型与算力：BGE 编码/重排/GPU 资源保障，QoS 限流与降级；
- 权限与合规：ABAC 标签一致性；日志审计与脱敏策略闭环；
- 数据质量：OCR 低质页、扫描件、图片表格；需要抽检与回溯机制。

---

## 六、落地清单（摘）

- 规格与接口：检索/重排/生成/评估/FAQ/画像/预热接口定义；
- 配置与权重：召回权重、重排阈值、缓存 TTL、分层规则参数化；
- 可观测性：Tracing、结构化日志、失败样本自动采集。

（本文件与《开发设计.md》配套使用：前者给出架构与路线，后者给出实现与接口细节。）



## 七、优化维度与改进建议（中英结合，可迭代）

- **精准检索（Exact Match & Structured Search）**
  - 关键词精确查找：`Term/Exact/Keyword` 字段、短语匹配（`phrase_match`）、邻近度（`slop`）、布尔组合（`must/should/filter`）、字段权重（`boost`）。
  - 结构化过滤：按 产品/版本/特性/命令/文档类型/时间窗 等元数据过滤与排序；标题/章节精确匹配优先。
  - 同义词与词形：领域同义词（`synonyms`）与缩写库（单/双向）提升召回与精度。
  - 落地动作：优化 ES 分词器（`standard`+自定义）、保留 `keyword` 字段、配置短语与邻近、建设领域同义词与缩写库。

- **缩小检索范围（Scope Narrowing）**
  - 用户画像（User Profile）：部门/岗位/产品线/常用版本/地域，作为默认过滤或权重提升；会话上下文（Session Context）保持主题锁定。
  - 权限与 ABAC：基于访问控制标签（`ABAC`）在索引侧与查询侧双重过滤，避免无关候选进入重排。
  - 热点与语义缓存（Chunk/Semantic Cache）：高频问题与热点切片优先命中；结合 `Preloader（预加载）` 做热门/下一问预测预热。
  - 时间/版本窗口（Time/Version Window）：默认近期版本与近一段时间窗口，用户可显式放宽。
  - 落地动作：画像与 ACL 标签落库、查询模板参数化、TopN 热门查询与新版本发布时的批量预热策略。

- **召回增强（Recall Enhancement）**
  - 多路召回：稀疏（`BM25/SPLADE`）+ 密集（`BGE` 向量）+ 图谱（`KG` 属性/路径）并行；分通道打分融合（`Fusion/RRF`）。
  - 多切片/多向量：标题、正文、表格分通道编码；章节感知切片策略（滑窗/段落合并）。
  - 落地动作：可选引入 `SPLADE`；向量检索参数优化（如 Milvus/HNSW 的 `efSearch/M/nprobe`）；切片策略调优。

- **查询改写（Query Rewrite & Disambiguation）**
  - 词法/语义归一化、拼写纠错、同义词/缩写扩展、实体识别与消歧（`NER+KG`）、意图识别与模板化改写。
  - 落地动作：领域词表/缩写库维护；图谱驱动的消歧；为重排与生成写入可解释的改写轨迹（Trace）。

- **重排优化（Reranking Optimization）**
  - 交叉编码器（`Cross-Encoder`）校准；引入位置/标题匹配/图谱距离/摘要覆盖率/新鲜度等特征；融合策略（线性/轻量树模型）。
  - 落地动作：蒸馏轻量重排模型；特征工程与权重自动化调参；对齐线下与线上评估分布。

- **生成与事实校验（AnswerGen & Fact Verification）**
  - 引用强制（Citations）、片段内回答（Chunk-bounded Generation）、事实一致性评分阈值与失败回退路径（Fallback）。
  - 落地动作：自检提示词（Self-check Prompt）、多证据交叉验证、不可证实时提示换检索或追问。

- **性能与时延（Performance & Latency）**
  - 缓存优化：语义缓存命中率提升、分层 TTL、近线重算（Nearline Refresh）、缓存键包含 `语义哈希+ACL+版本` 维度。
  - 检索参数：向量召回参数自适应、ES 路由与副本策略、并发与批处理（Batching/Streaming）。
  - 落地动作：重点观测 `P95 首字/P99 端到端/缓存命中率`，基于 SLA 做动态降级（如降通道/降权重/缩 TopK）。

- **成本优化（Cost Efficiency）**
  - 冷热分层（`Tiering`）驱动存储/索引/缓存资源配比；索引压缩、字段裁剪、按层级调整副本数与刷新频率。
  - 落地动作：分层策略与成本模型联动，月度成本看板与阈值告警。

- **可观测与运营（Observability & Ops）**
  - A/B 实验覆盖策略/权重/提示词，失败样本自动采集与回放，离线回归集持续扩充。
  - 落地动作：埋点标准化、Tracing 全链路、自动产出问题 TopN 与漏斗分析报告。
