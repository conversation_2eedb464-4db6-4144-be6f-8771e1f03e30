# 知识图谱表结构文档

## 概述

本文档详细描述了ZTE产品技术问答系统中使用的知识图谱数据库结构，包括实体类型、关系定义、字段说明以及查询模式。

### 基本信息
- **图数据库**: Nebula Graph
- **Space名称**: `product_multiversion`
- **访问端点**: https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/nebula/executeNql
- **认证方式**: X-Signature签名认证

---

## 实体（顶点）结构

### 1. 产品实体 (product)

| 字段名 | 数据类型 | 描述 | 示例 |
|--------|----------|------|------|
| name | string | 产品名称 | "ZXR10 M6000-8S Plus" |
| product_id | string | 产品唯一标识 | "product123456" |
| series_id | string | 所属系列ID | "series789" |

**标签**: `product`

**用途**: 表示具体的产品型号，是图谱中的核心实体

### 2. 系列实体 (series)

| 字段名 | 数据类型 | 描述 | 示例 |
|--------|----------|------|------|
| name | string | 系列名称 | "ZXR10 M6000系列" |
| series_id | string | 系列唯一标识 | "series789" |

**标签**: `series`

**用途**: 表示产品系列，用于组织和分类相关产品

### 3. 版本实体 (product_version)

| 字段名 | 数据类型 | 描述 | 示例 |
|--------|----------|------|------|
| version | string | 版本号 | "V2.1" |
| release_date | date | 发布日期 | "2024-01-15" |

**标签**: `product_version`

**用途**: 表示产品的具体版本信息

### 4. 文档实体

#### 4.1 文档实体 (document)

| 字段名 | 数据类型 | 描述 | 示例 |
|--------|----------|------|------|
| id | string | 文档唯一标识 | "doc_001" |
| content | string | 文档内容/名称 | "用户手册" |
| document_name | string | 文档名称 | "ZXR10_M6000_用户手册.pdf" |

**标签**: `document`

#### 4.2 文本实体 (text)

| 字段名 | 数据类型 | 描述 | 示例 |
|--------|----------|------|------|
| id | string | 文本唯一标识 | "text_001" |
| content | string | 文本内容 | "产品配置说明..." |

**标签**: `text`

#### 4.3 表格实体 (table)

| 字段名 | 数据类型 | 描述 | 示例 |
|--------|----------|------|------|
| id | string | 表格唯一标识 | "table_001" |
| content | string | 表格内容描述 | "技术参数表" |

**标签**: `table`

---

## 关系（边）结构

### 1. 产品相关关系

#### 1.1 产品-文档关系 (product_document_relation)
- **源实体**: product
- **目标实体**: document/text/table
- **描述**: 产品直接关联的文档
- **使用场景**: 查找产品相关的技术文档、用户手册等

#### 1.2 产品-版本关系 (product_version_relation)
- **源实体**: product
- **目标实体**: product_version
- **描述**: 产品包含的版本信息
- **使用场景**: 获取产品的所有版本

#### 1.3 产品兼容关系 (product_compatible_relation)
- **源实体**: product
- **目标实体**: product
- **描述**: 产品之间的兼容性关系
- **使用场景**: 查找兼容的产品型号

#### 1.4 产品升级关系 (product_upgrade_relation)
- **源实体**: product
- **目标实体**: product
- **描述**: 产品升级路径
- **使用场景**: 查找产品的升级选项

### 2. 系列相关关系

#### 2.1 系列-文档关系 (series_document_relation)
- **源实体**: series
- **目标实体**: document/text/table
- **描述**: 系列级别的文档关联
- **使用场景**: 获取整个产品系列的通用文档

#### 2.2 系列-产品关系 (series_product_relation)
- **源实体**: series
- **目标实体**: product
- **描述**: 系列包含的产品
- **使用场景**: 查找系列下的所有产品

#### 2.3 系列-版本关系 (series_version_relation)
- **源实体**: series
- **目标实体**: product_version
- **描述**: 系列与版本的关联
- **使用场景**: 获取系列支持的版本信息

### 3. 版本相关关系

#### 3.1 版本-文档关系 (productversion_document_relation)
- **源实体**: product_version
- **目标实体**: document/text/table
- **描述**: 特定版本的文档关联
- **使用场景**: 获取特定版本的文档

#### 3.2 版本-文档关系 (version_document_relation)
- **源实体**: product_version
- **目标实体**: document
- **描述**: 版本文档关联的另一种表示
- **使用场景**: 版本文档查询的备用关系

### 4. 文档相关关系

#### 4.1 文档-文本关系 (document_text_relation)
- **源实体**: document
- **目标实体**: text
- **描述**: 文档包含的文本内容
- **使用场景**: 文档内容的细粒度访问

#### 4.2 文档-表格关系 (document_table_relation)
- **源实体**: document
- **目标实体**: table
- **描述**: 文档包含的表格内容
- **使用场景**: 获取文档中的结构化数据

#### 4.3 文档-章节关系 (document_chapter_relation)
- **源实体**: document
- **目标实体**: chapter
- **描述**: 文档的章节结构
- **使用场景**: 按章节组织文档内容

#### 4.4 文本-表格关系 (text_table_relation)
- **源实体**: text
- **目标实体**: table
- **描述**: 文本与表格的关联
- **使用场景**: 文本说明对应的表格数据

---

## 核心查询模式

### 1. 产品文档查询

**目的**: 根据产品ID查找直接关联的文档

```cypher
GO FROM '{product_id}' OVER product_document_relation 
YIELD dst(edge) as destination | 
fetch prop on * $-.destination YIELD vertex as vertices_;
```

**实现文件**: `retrieve/kg_retrieve/kg_retrieve.py:body_relation_doc_pid()`

### 2. 系列文档查询

**目的**: 根据系列ID查找系列级别的文档

```cypher
GO FROM '{series_id}' OVER series_document_relation 
YIELD dst(edge) as destination |
fetch prop on * $-.destination YIELD vertex as vertices_;
```

**实现文件**: `retrieve/kg_retrieve/kg_retrieve.py:body_relation_doc_sid()`

### 3. 版本文档查询

**目的**: 根据产品/系列和版本号查找特定版本的文档

#### 3.1 系列版本文档查询
```cypher
MATCH (p:series)-[:series_version_relation]->(pv:product_version)-[:productversion_document_relation]->(pd:document) 
WHERE id(p) == '{series_id}' and pv.product_version.version CONTAINS '{version}' 
RETURN pd;
```

#### 3.2 产品版本文档查询
```cypher
MATCH (p:product)-[:product_version_relation]->(pv:product_version)-[:productversion_document_relation]->(pd:document) 
WHERE id(p) == '{product_id}' and pv.product_version.version CONTAINS '{version}' 
RETURN pd;
```

**实现文件**: `retrieve/kg_retrieve/kg_retrieve.py:body_relation_doc_version()`

### 4. 通用图谱查询

**目的**: 执行任意NQL查询

```cypher
MATCH (v:{entity_type}) WHERE v.{property}=='{value}' RETURN v LIMIT 5
```

**实现文件**: `retrieve/kg_retrieve/kg_retrieve.py:query_data_kg()`

---

## 字段映射关系

项目中使用了中英文字段映射（定义在`domain/constants/infoGo_constants.py`），主要包括：

| 中文字段 | 英文字段 | 用途 |
|----------|----------|------|
| 系列产品联系 | series_product_relation | 系列与产品的关联 |
| 产品文档联系 | product_document_relation | 产品与文档的关联 |
| 系列文档联系 | series_document_relation | 系列与文档的关联 |
| 文档文本联系 | document_text_relation | 文档与文本的关联 |
| 文档表联系 | document_table_relation | 文档与表格的关联 |
| 文档章节联系 | document_chapter_relation | 文档与章节的关联 |
| 词条内容联系 | entry_attribute_relation | 词条与属性的关联 |
| 文本表联系 | text_table_relation | 文本与表格的关联 |

---

## 数据访问配置

### 1. 连接配置

```python
{
    "kg": {
        "space": "product_multiversion",
        "kg_url": "https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/nebula/executeNql"
    }
}
```

### 2. 认证配置

```python
{
    "Knowledge": {
        "access_environment": "环境标识",
        "access_key": "访问密钥",
        "access_secret": "访问秘钥",
        "es_headers": {
            "systemName": "系统名称"
        }
    }
}
```

### 3. 请求格式

```json
{
    "space": "product_multiversion",
    "nql": "具体的查询语句"
}
```

### 4. 响应格式

```json
{
    "bo": [
        {
            "vertices_": {
                "id": "实体ID",
                "vertexLabel": "实体标签",
                "properties": {
                    "property1": "值1",
                    "property2": "值2"
                }
            }
        }
    ]
}
```

---

## 查询优化建议

### 1. 索引优化
- 在高频查询的属性上建立索引（如product.name, series.name）
- 对ID字段建立唯一索引

### 2. 查询优化
- 使用LIMIT限制返回结果数量
- 优先使用精确匹配而非模糊匹配
- 合理使用路径长度限制

### 3. 缓存策略
- 对热点查询结果进行缓存
- 实施查询结果的分级缓存

---

## 扩展方向

### 1. 新增实体类型
- **技术特性实体** (feature): 存储产品技术特性
- **兼容性实体** (compatibility): 详细的兼容性信息
- **故障实体** (fault): 故障诊断相关信息

### 2. 新增关系类型
- **依赖关系** (depends_on): 技术依赖关系
- **替代关系** (替代_with): 产品替代关系
- **包含关系** (contains): 层次包含关系

### 3. 属性扩展
- 增加时间戳属性用于版本控制
- 增加权重属性用于关系重要性排序
- 增加标签属性用于分类和过滤

---

## 维护说明

### 1. 数据更新
- 定期同步产品信息更新
- 维护版本发布时的关系更新
- 文档变更时的关联关系更新

### 2. 性能监控
- 监控查询响应时间
- 跟踪热点查询模式
- 定期优化图谱结构

### 3. 数据质量
- 定期检查数据一致性
- 验证关系的完整性
- 清理无效的孤立节点

---

*最后更新: 2025-08-13*
*版本: 1.0*